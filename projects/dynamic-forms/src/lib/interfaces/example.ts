import { IFormConfig } from '.';

// Complete Example with embedded fields
const employeeFormExample: IFormConfig = {
  id: 'employee-onboarding',
  version: '2.0.0',
  metadata: {
    title: 'Employee Onboarding',
    description: 'Complete employee onboarding form',
    estimatedTime: '15-20 minutes',
  },
  settings: {
    autoSave: true,
    autoSaveInterval: 30000,
    showProgressBar: true,
    validateOnBlur: true,
    scrollToError: true,
  },
  layout: {
    type: 'wizard',
    orientation: 'horizontal',
    steps: [
      {
        id: 'personal-info',
        title: 'Personal Information',
        icon: 'user',
        sections: [
          {
            id: 'basic-info',
            title: 'Basic Information',
            rows: [
              {
                id: 'name-row',
                columns: [
                  {
                    width: { xs: 12, md: 4 },
                    fields: [
                      {
                        id: 'firstName',
                        name: 'firstName',
                        type: 'text',
                        label: 'First Name',
                        placeholder: 'Enter first name',
                        required: true,
                        validation: [
                          {
                            type: 'required',
                            message: 'First name is required',
                          },
                          { type: 'minLength', value: 2 },
                          { type: 'pattern', pattern: '^[a-zA-Z]+$' },
                        ],
                        transform: {
                          input: 'trim',
                          output: 'capitalize',
                        },
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 4 },
                    fields: [
                      {
                        id: 'middleName',
                        name: 'middleName',
                        type: 'text',
                        label: 'Middle Name',
                        placeholder: 'Enter middle name',
                        required: false,
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 4 },
                    fields: [
                      {
                        id: 'lastName',
                        name: 'lastName',
                        type: 'text',
                        label: 'Last Name',
                        placeholder: 'Enter last name',
                        required: true,
                        validation: [
                          {
                            type: 'required',
                            message: 'Last name is required',
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                id: 'contact-row',
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'email',
                        name: 'email',
                        type: 'email',
                        label: 'Email Address',
                        placeholder: '<EMAIL>',
                        required: true,
                        validation: [{ type: 'required' }, { type: 'email' }],
                        asyncValidation: [
                          {
                            type: 'unique',
                            endpoint: '/api/validate/email',
                            debounce: 500,
                            message: 'Email already exists',
                          },
                        ],
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'phone',
                        name: 'phone',
                        type: 'tel',
                        label: 'Phone Number',
                        placeholder: '+****************',
                        mask: '+****************',
                        required: true,
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            id: 'address-section',
            title: 'Address Information',
            collapsible: true,
            rows: [
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'address',
                        name: 'address',
                        type: 'group',
                        label: 'Current Address',
                        groupConfig: {
                          bordered: true,
                          rows: [
                            {
                              columns: [
                                {
                                  width: 12,
                                  fields: [
                                    {
                                      id: 'street',
                                      name: 'street',
                                      type: 'text',
                                      label: 'Street Address',
                                      required: true,
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              columns: [
                                {
                                  width: { xs: 12, md: 6 },
                                  fields: [
                                    {
                                      id: 'city',
                                      name: 'city',
                                      type: 'text',
                                      label: 'City',
                                      required: true,
                                    },
                                  ],
                                },
                                {
                                  width: { xs: 12, md: 3 },
                                  fields: [
                                    {
                                      id: 'state',
                                      name: 'state',
                                      type: 'select',
                                      label: 'State',
                                      required: true,
                                      options: {
                                        type: 'api',
                                        url: '/api/states',
                                        valueField: 'code',
                                        labelField: 'name',
                                      },
                                    },
                                  ],
                                },
                                {
                                  width: { xs: 12, md: 3 },
                                  fields: [
                                    {
                                      id: 'zipCode',
                                      name: 'zipCode',
                                      type: 'text',
                                      label: 'ZIP Code',
                                      mask: '99999',
                                      required: true,
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'employment',
        title: 'Employment Details',
        icon: 'briefcase',
        sections: [
          {
            id: 'position-info',
            title: 'Position Information',
            rows: [
              {
                columns: [
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'department',
                        name: 'department',
                        type: 'select',
                        label: 'Department',
                        required: true,
                        options: {
                          type: 'api',
                          url: '/api/departments',
                          cache: true,
                        },
                      },
                    ],
                  },
                  {
                    width: { xs: 12, md: 6 },
                    fields: [
                      {
                        id: 'position',
                        name: 'position',
                        type: 'select',
                        label: 'Position',
                        required: true,
                        disabled: true,
                        condition: {
                          field: 'department',
                          operator: 'notEmpty',
                        },
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'qualifications',
        title: 'Qualifications',
        icon: 'certificate',
        sections: [
          {
            id: 'education',
            title: 'Education',
            rows: [
              {
                columns: [
                  {
                    width: 12,
                    fields: [
                      {
                        id: 'education',
                        name: 'education',
                        type: 'array',
                        label: 'Educational Background',
                        arrayConfig: {
                          minItems: 1,
                          maxItems: 5,
                          addButton: {
                            text: 'Add Education',
                            icon: 'plus',
                          },
                          sortable: true,
                          collapsible: true,
                          template: {
                            rows: [
                              {
                                columns: [
                                  {
                                    width: { xs: 12, md: 6 },
                                    fields: [
                                      {
                                        id: 'degree',
                                        name: 'degree',
                                        type: 'select',
                                        label: 'Degree',
                                        required: true,
                                        options: [
                                          {
                                            value: 'bachelors',
                                            label: "Bachelor's",
                                          },
                                          {
                                            value: 'masters',
                                            label: "Master's",
                                          },
                                          { value: 'phd', label: 'PhD' },
                                        ],
                                      },
                                    ],
                                  },
                                  {
                                    width: { xs: 12, md: 6 },
                                    fields: [
                                      {
                                        id: 'institution',
                                        name: 'institution',
                                        type: 'text',
                                        label: 'Institution',
                                        required: true,
                                      },
                                    ],
                                  },
                                ],
                              },
                              {
                                columns: [
                                  {
                                    width: { xs: 12, md: 4 },
                                    fields: [
                                      {
                                        id: 'fieldOfStudy',
                                        name: 'fieldOfStudy',
                                        type: 'text',
                                        label: 'Field of Study',
                                        required: true,
                                      },
                                    ],
                                  },
                                  {
                                    width: { xs: 12, md: 4 },
                                    fields: [
                                      {
                                        id: 'graduationYear',
                                        name: 'graduationYear',
                                        type: 'number',
                                        label: 'Graduation Year',
                                        min: 1950,
                                        max: 2030,
                                        required: true,
                                      },
                                    ],
                                  },
                                  {
                                    width: { xs: 12, md: 4 },
                                    fields: [
                                      {
                                        id: 'gpa',
                                        name: 'gpa',
                                        type: 'number',
                                        label: 'GPA',
                                        min: 0,
                                        max: 4.0,
                                        step: 0.1,
                                        precision: 2,
                                      },
                                    ],
                                  },
                                ],
                              },
                            ],
                          },
                        },
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  conditionals: [
    {
      id: 'show-permanent-address',
      condition: {
        field: 'sameAsCurrent',
        operator: 'equals',
        value: false,
      },
      actions: [
        {
          type: 'show',
          target: 'permanentAddress',
        },
      ],
    },
  ],
  hooks: {
    onInit: async (form) => {
      // Initialize form
    },
    beforeSubmit: async (values) => {
      // Transform values before submit
      return values;
    },
    afterSubmit: async (response) => {
      // Handle after submit
    },
    onError: (error) => {
      // Handle errors
    },
  },
};
