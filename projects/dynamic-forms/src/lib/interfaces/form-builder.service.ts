// core/services/form-builder.service.ts

import { Injectable } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormArray,
  FormControl,
  AbstractControl,
  ValidatorFn,
  AsyncValidatorFn,
} from '@angular/forms';
import { ValidatorRegistry } from './validator-registry.service';
import { FieldRegistry } from './field-registry.service';
import { FormContext } from './form-context.service';
import { IArrayConfig, IField, IFormConfig, IRow, ISection } from '.';

@Injectable({
  providedIn: 'root',
})
export class FormBuilderService {
  constructor(
    private readonly fb: FormBuilder,
    private readonly validatorRegistry: ValidatorRegistry,
    private readonly fieldRegistry: FieldRegistry,
    private readonly formContext: FormContext
  ) {}

  public buildForm(config: IFormConfig): FormGroup {
    this.fieldRegistry.clear();

    const formGroup = this.fb.group({});
    const fields = this.extractFieldsFromLayout(config.layout);

    this.buildFormControls(formGroup, fields);

    this.formContext.initialize(formGroup, config);

    if (config.hooks?.onInit) {
      Promise.resolve(config.hooks.onInit(formGroup));
    }

    return formGroup;
  }

  private extractFieldsFromLayout(layout: any): IField[] {
    const fields: IField[] = [];

    if (layout.sections) {
      layout.sections.forEach((section: ISection) => {
        fields.push(...this.extractFieldsFromSection(section));
      });
    }

    if (layout.steps) {
      layout.steps.forEach((step: any) => {
        step.sections?.forEach((section: ISection) => {
          fields.push(...this.extractFieldsFromSection(section));
        });
      });
    }

    if (layout.tabs) {
      layout.tabs.forEach((tab: any) => {
        tab.sections?.forEach((section: ISection) => {
          fields.push(...this.extractFieldsFromSection(section));
        });
      });
    }

    if (layout.panels) {
      layout.panels.forEach((panel: any) => {
        panel.sections?.forEach((section: ISection) => {
          fields.push(...this.extractFieldsFromSection(section));
        });
      });
    }

    return fields;
  }

  private extractFieldsFromSection(section: ISection): IField[] {
    const fields: IField[] = [];

    section.rows.forEach((row) => {
      row.columns.forEach((column) => {
        fields.push(...column.fields);
      });
    });

    return fields;
  }

  private buildFormControls(
    formGroup: FormGroup,
    fields: IField[],
    parentPath: string = ''
  ): void {
    fields.forEach((field) => {
      const path = parentPath ? `${parentPath}.${field.name}` : field.name;
      const control = this.createControl(field, formGroup);

      if (control) {
        this.addControlToForm(formGroup, field.name, control);
        this.fieldRegistry.registerField(field, control, path);
      }
    });
  }

  private createControl(
    field: IField,
    parentForm: FormGroup
  ): AbstractControl | null {
    switch (field.type) {
      case 'group':
        return this.createGroupControl(field);
      case 'array':
      case 'repeater':
        return this.createArrayControl(field);
      case 'heading':
      case 'paragraph':
      case 'divider':
      case 'spacer':
      case 'alert':
      case 'info':
        return null;
      default:
        return this.createFormControl(field, parentForm);
    }
  }

  private createFormControl(field: IField, parentForm: FormGroup): FormControl {
    const validators = this.buildValidators(field);
    const asyncValidators = this.buildAsyncValidators(field);

    const control = this.fb.control(
      {
        value: this.getInitialValue(field),
        disabled: this.isInitiallyDisabled(field),
      },
      {
        validators,
        asyncValidators,
        updateOn: this.getUpdateOn(field),
      }
    );

    this.setupControlSubscriptions(control, field, parentForm);

    return control;
  }

  private createGroupControl(field: IField): FormGroup {
    const group = this.fb.group({});

    if (field.groupConfig?.rows) {
      const nestedFields = this.extractFieldsFromRows(field.groupConfig.rows);
      this.buildFormControls(group, nestedFields, field.name);
    }

    return group;
  }

  private createArrayControl(field: IField): FormArray {
    const array = this.fb.array([]);

    if (field.arrayConfig) {
      const initialCount = field.arrayConfig.initialItems || 0;
      for (let i = 0; i < initialCount; i++) {
        array.push(this.createArrayItem(field.arrayConfig));
      }
    }

    return array;
  }

  private createArrayItem(config: IArrayConfig): AbstractControl {
    if (config.template?.rows) {
      const group = this.fb.group({});
      const fields = this.extractFieldsFromRows(config.template.rows);
      this.buildFormControls(group, fields);
      return group;
    }

    return this.fb.control(null);
  }

  private extractFieldsFromRows(rows: IRow[]): IField[] {
    const fields: IField[] = [];

    rows.forEach((row) => {
      row.columns.forEach((column) => {
        fields.push(...column.fields);
      });
    });

    return fields;
  }

  private buildValidators(field: IField): ValidatorFn[] {
    const validators: ValidatorFn[] = [];

    if (field.required === true) {
      const requiredValidator = this.validatorRegistry.getValidator('required');
      if (requiredValidator) {
        validators.push(requiredValidator());
      }
    }

    if (field.validation) {
      const fieldValidators = this.validatorRegistry.buildValidators(
        field.validation.map((v) => ({
          name: v.type,
          params: v.value || v.params,
          message: v.message,
        }))
      );
      validators.push(...fieldValidators);
    }

    if (field.minLength) {
      const validator = this.validatorRegistry.getValidator('minLength');
      if (validator) validators.push(validator(field.minLength));
    }

    if (field.maxLength) {
      const validator = this.validatorRegistry.getValidator('maxLength');
      if (validator) validators.push(validator(field.maxLength));
    }

    if (field.min !== undefined) {
      const validator = this.validatorRegistry.getValidator('min');
      if (validator) validators.push(validator(field.min));
    }

    if (field.max !== undefined) {
      const validator = this.validatorRegistry.getValidator('max');
      if (validator) validators.push(validator(field.max));
    }

    if (field.pattern) {
      const validator = this.validatorRegistry.getValidator('pattern');
      if (validator) validators.push(validator(field.pattern));
    }

    return validators;
  }

  private buildAsyncValidators(field: IField): AsyncValidatorFn[] {
    if (!field.asyncValidation) return [];

    return this.validatorRegistry.buildAsyncValidators(
      field.asyncValidation.map((v) => ({
        name: v.type,
        params: v.params,
        message: v.message,
        debounce: v.debounce,
        endpoint: v.endpoint,
      }))
    );
  }

  private getInitialValue(field: IField): any {
    if (field.value !== undefined) return field.value;
    if (field.defaultValue !== undefined) return field.defaultValue;

    switch (field.type) {
      case 'checkbox':
      case 'toggle':
      case 'switch':
        return false;
      case 'number':
      case 'range':
      case 'rating':
      case 'slider':
        return field.min || 0;
      case 'multiselect':
      case 'tags':
      case 'chips':
        return [];
      case 'date':
      case 'datetime':
      case 'time':
        return null;
      default:
        return '';
    }
  }

  private isInitiallyDisabled(field: IField): boolean {
    if (typeof field.disabled === 'boolean') {
      return field.disabled;
    }
    if (typeof field.readonly === 'boolean') {
      return field.readonly;
    }
    return false;
  }

  private getUpdateOn(field: IField): 'change' | 'blur' | 'submit' {
    if (field.validation?.some((v) => v.validateOn === 'blur')) {
      return 'blur';
    }
    if (field.validation?.some((v) => v.validateOn === 'submit')) {
      return 'submit';
    }
    return 'change';
  }

  private addControlToForm(
    form: FormGroup,
    name: string,
    control: AbstractControl
  ): void {
    const nameParts = name.split('.');

    if (nameParts.length === 1) {
      form.addControl(name, control);
    } else {
      let currentForm = form;

      for (let i = 0; i < nameParts.length - 1; i++) {
        const part = nameParts[i];

        if (!currentForm.controls[part]) {
          currentForm.addControl(part, this.fb.group({}));
        }

        currentForm = currentForm.controls[part] as FormGroup;
      }

      currentForm.addControl(nameParts[nameParts.length - 1], control);
    }
  }

  private setupControlSubscriptions(
    control: AbstractControl,
    field: IField,
    parentForm: FormGroup
  ): void {
    if (field.dependencies) {
      field.dependencies.forEach((dep) => {
        const depControl = parentForm.get(dep.field);
        if (depControl) {
          depControl.valueChanges.subscribe((value) => {
            this.handleDependencyChange(control, field, dep, value);
          });
        }
      });
    }

    if (field.events?.onChange) {
      control.valueChanges.subscribe((value) => {
        this.handleFieldEvent(field.events!.onChange!, {
          type: 'change',
          field,
          value,
          previousValue: null,
          form: parentForm,
          timestamp: Date.now(),
        });
      });
    }
  }

  private handleDependencyChange(
    control: AbstractControl,
    field: IField,
    dependency: any,
    value: any
  ): void {
    switch (dependency.type) {
      case 'visibility':
        if (value) {
          control.enable();
        } else {
          control.disable();
        }
        break;
      case 'required':
        if (value) {
          control.addValidators(
            this.validatorRegistry.getValidator('required')!()
          );
        } else {
          control.removeValidators(
            this.validatorRegistry.getValidator('required')!()
          );
        }
        control.updateValueAndValidity();
        break;
      case 'value':
        const transformedValue = dependency.transform
          ? dependency.transform(value)
          : value;
        control.setValue(transformedValue);
        break;
    }
  }

  private handleFieldEvent(handler: any, event: any): void {
    if (handler.handler) {
      handler.handler(event);
    }
  }

  public addArrayItem(arrayPath: string, index?: number): void {
    const control = this.formContext.getControl(arrayPath);
    if (!(control instanceof FormArray)) return;

    const field = this.fieldRegistry.getFieldByPath(arrayPath);
    if (!field?.field.arrayConfig) return;

    const newItem = this.createArrayItem(field.field.arrayConfig);

    if (index !== undefined) {
      control.insert(index, newItem);
    } else {
      control.push(newItem);
    }
  }

  public removeArrayItem(arrayPath: string, index: number): void {
    const control = this.formContext.getControl(arrayPath);
    if (!(control instanceof FormArray)) return;

    control.removeAt(index);
  }

  public moveArrayItem(arrayPath: string, from: number, to: number): void {
    const control = this.formContext.getControl(arrayPath);
    if (!(control instanceof FormArray)) return;

    const item = control.at(from);
    control.removeAt(from);
    control.insert(to, item);
  }

  public duplicateArrayItem(arrayPath: string, index: number): void {
    const control = this.formContext.getControl(arrayPath);
    if (!(control instanceof FormArray)) return;

    const item = control.at(index);
    const clonedItem = this.cloneControl(item);
    control.insert(index + 1, clonedItem);
  }

  private cloneControl(control: AbstractControl): AbstractControl {
    if (control instanceof FormControl) {
      return this.fb.control(
        control.value,
        control.validator,
        control.asyncValidator
      );
    } else if (control instanceof FormGroup) {
      const group = this.fb.group({});
      Object.keys(control.controls).forEach((key) => {
        group.addControl(key, this.cloneControl(control.controls[key]));
      });
      return group;
    } else if (control instanceof FormArray) {
      const array = this.fb.array([]);
      control.controls.forEach((c) => {
        array.push(this.cloneControl(c));
      });
      return array;
    }
    return control;
  }

  public validateForm(): boolean {
    const form = this.formContext.getForm();
    if (!form) return false;

    this.markAllAsTouched(form);
    form.updateValueAndValidity();

    return form.valid;
  }

  private markAllAsTouched(control: AbstractControl): void {
    control.markAsTouched();

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((key) => {
        this.markAllAsTouched(control.controls[key]);
      });
    } else if (control instanceof FormArray) {
      control.controls.forEach((c) => this.markAllAsTouched(c));
    }
  }

  public resetForm(values?: any): void {
    const form = this.formContext.getForm();
    if (form) {
      form.reset(values);
    }
  }

  public getFormValues(): any {
    return this.formContext.getForm()?.value;
  }

  public setFormValues(values: any): void {
    this.formContext.patchValue(values);
  }

  public getFormErrors(): any {
    return this.formContext.getState().errors;
  }

  public isFormValid(): boolean {
    return this.formContext.isValid();
  }

  public isFormDirty(): boolean {
    return this.formContext.isDirty();
  }

  public submitForm(): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.validateForm()) {
        reject({ error: 'Validation failed', errors: this.getFormErrors() });
        return;
      }

      this.formContext.setSubmitting(true);
      const values = this.getFormValues();

      const config = this.formContext.getConfig();
      if (config?.hooks?.beforeSubmit) {
        Promise.resolve(
          config.hooks.beforeSubmit(values, this.formContext.getForm())
        )
          .then((transformedValues) => {
            this.formContext.incrementSubmitCount();
            resolve(transformedValues || values);
          })
          .catch(reject)
          .finally(() => {
            this.formContext.setSubmitting(false);
          });
      } else {
        this.formContext.incrementSubmitCount();
        this.formContext.setSubmitting(false);
        resolve(values);
      }
    });
  }
}
