import { Injectable } from '@angular/core';
import { FormGroup, FormArray, AbstractControl } from '@angular/forms';
import { BehaviorSubject, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { IFormConfig, IFormState } from '.';

export interface FormContextState {
  form: FormGroup | null;
  config: IFormConfig | null;
  state: IFormState;
  metadata: Record<string, any>;
}

export interface FormValueChange {
  path: string;
  value: any;
  previousValue: any;
  source: 'user' | 'api' | 'system';
}

@Injectable()
export class FormContext {
  private formGroup: FormGroup | null = null;
  private formConfig: IFormConfig | null = null;

  private readonly stateSubject = new BehaviorSubject<IFormState>({
    values: {},
    errors: {},
    touched: {},
    dirty: {},
    disabled: {},
    hidden: {},
    isValid: false,
    isSubmitting: false,
    isValidating: false,
    submitCount: 0,
  });

  private readonly valueChangesSubject = new Subject<FormValueChange>();
  private readonly contextSubject = new BehaviorSubject<FormContextState>({
    form: null,
    config: null,
    state: this.stateSubject.value,
    metadata: {},
  });

  public state$ = this.stateSubject.asObservable();
  public valueChanges$ = this.valueChangesSubject.asObservable();
  public context$ = this.contextSubject.asObservable();

  private subscriptions: any[] = [];
  private readonly metadata = new Map<string, any>();

  public initialize(form: FormGroup, config: IFormConfig): void {
    this.cleanup();

    this.formGroup = form;
    this.formConfig = config;

    this.setupFormSubscriptions();
    this.updateContext();
    this.updateState();
  }

  private setupFormSubscriptions(): void {
    if (!this.formGroup) return;

    const valueChanges = this.formGroup.valueChanges
      .pipe(debounceTime(100), distinctUntilChanged())
      .subscribe((values) => {
        this.updateState();
        this.emitValueChanges(
          '',
          values,
          this.stateSubject.value.values,
          'user'
        );
      });

    const statusChanges = this.formGroup.statusChanges.subscribe(() => {
      this.updateState();
    });

    this.subscriptions.push(valueChanges, statusChanges);
  }

  private updateState(): void {
    if (!this.formGroup) return;

    const state: IFormState = {
      values: this.formGroup.value,
      errors: this.collectErrors(this.formGroup),
      touched: this.collectTouchedState(this.formGroup),
      dirty: this.collectDirtyState(this.formGroup),
      disabled: this.collectDisabledState(this.formGroup),
      hidden: this.collectHiddenState(),
      isValid: this.formGroup.valid,
      isSubmitting: this.stateSubject.value.isSubmitting,
      isValidating: this.stateSubject.value.isValidating,
      submitCount: this.stateSubject.value.submitCount,
      currentStep: this.stateSubject.value.currentStep,
      completedSteps: this.stateSubject.value.completedSteps,
      visitedSteps: this.stateSubject.value.visitedSteps,
    };

    this.stateSubject.next(state);
    this.updateContext();
  }

  private updateContext(): void {
    const metadataObj: Record<string, any> = {};
    this.metadata.forEach((value, key) => {
      metadataObj[key] = value;
    });

    this.contextSubject.next({
      form: this.formGroup,
      config: this.formConfig,
      state: this.stateSubject.value,
      metadata: metadataObj,
    });
  }

  private collectErrors(
    control: AbstractControl,
    path: string = ''
  ): Record<string, any> {
    const errors: Record<string, any> = {};

    if (control.errors && control.touched) {
      errors[path || 'root'] = control.errors;
    }

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((key) => {
        const childPath = path ? `${path}.${key}` : key;
        Object.assign(
          errors,
          this.collectErrors(control.controls[key], childPath)
        );
      });
    } else if (control instanceof FormArray) {
      control.controls.forEach((item, index) => {
        const childPath = path ? `${path}.${index}` : `${index}`;
        Object.assign(errors, this.collectErrors(item, childPath));
      });
    }

    return errors;
  }

  private collectTouchedState(
    control: AbstractControl,
    path: string = ''
  ): Record<string, boolean> {
    const touched: Record<string, boolean> = {};

    touched[path || 'root'] = control.touched;

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((key) => {
        const childPath = path ? `${path}.${key}` : key;
        Object.assign(
          touched,
          this.collectTouchedState(control.controls[key], childPath)
        );
      });
    } else if (control instanceof FormArray) {
      control.controls.forEach((item, index) => {
        const childPath = path ? `${path}.${index}` : `${index}`;
        Object.assign(touched, this.collectTouchedState(item, childPath));
      });
    }

    return touched;
  }

  private collectDirtyState(
    control: AbstractControl,
    path: string = ''
  ): Record<string, boolean> {
    const dirty: Record<string, boolean> = {};

    dirty[path || 'root'] = control.dirty;

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((key) => {
        const childPath = path ? `${path}.${key}` : key;
        Object.assign(
          dirty,
          this.collectDirtyState(control.controls[key], childPath)
        );
      });
    } else if (control instanceof FormArray) {
      control.controls.forEach((item, index) => {
        const childPath = path ? `${path}.${index}` : `${index}`;
        Object.assign(dirty, this.collectDirtyState(item, childPath));
      });
    }

    return dirty;
  }

  private collectDisabledState(
    control: AbstractControl,
    path: string = ''
  ): Record<string, boolean> {
    const disabled: Record<string, boolean> = {};

    disabled[path || 'root'] = control.disabled;

    if (control instanceof FormGroup) {
      Object.keys(control.controls).forEach((key) => {
        const childPath = path ? `${path}.${key}` : key;
        Object.assign(
          disabled,
          this.collectDisabledState(control.controls[key], childPath)
        );
      });
    } else if (control instanceof FormArray) {
      control.controls.forEach((item, index) => {
        const childPath = path ? `${path}.${index}` : `${index}`;
        Object.assign(disabled, this.collectDisabledState(item, childPath));
      });
    }

    return disabled;
  }

  private collectHiddenState(): Record<string, boolean> {
    return {};
  }

  private emitValueChanges(
    path: string,
    value: any,
    previousValue: any,
    source: 'user' | 'api' | 'system'
  ): void {
    this.valueChangesSubject.next({
      path,
      value,
      previousValue,
      source,
    });
  }

  public getValue(path: string): any {
    if (!this.formGroup) return undefined;

    const control = this.getControl(path);
    return control?.value;
  }

  public setValue(
    path: string,
    value: any,
    options?: { emitEvent?: boolean; source?: 'user' | 'api' | 'system' }
  ): void {
    const control = this.getControl(path);
    if (!control) return;

    const previousValue = control.value;
    control.setValue(value, { emitEvent: options?.emitEvent !== false });

    if (options?.emitEvent !== false) {
      this.emitValueChanges(
        path,
        value,
        previousValue,
        options?.source || 'system'
      );
    }
  }

  public patchValue(values: any, options?: { emitEvent?: boolean }): void {
    if (!this.formGroup) return;

    this.formGroup.patchValue(values, options);
  }

  public getControl(path: string): AbstractControl | null {
    if (!this.formGroup) return null;

    if (!path) return this.formGroup;

    const parts = path.split('.');
    let control: AbstractControl = this.formGroup;

    for (const part of parts) {
      if (control instanceof FormGroup) {
        control = control.controls[part];
      } else if (control instanceof FormArray) {
        const index = parseInt(part);
        control = control.at(index);
      }

      if (!control) return null;
    }

    return control;
  }

  public getError(path: string): any {
    const control = this.getControl(path);
    return control?.errors;
  }

  public setError(path: string, error: any): void {
    const control = this.getControl(path);
    if (!control) return;

    control.setErrors(error);
  }

  public clearError(path: string): void {
    const control = this.getControl(path);
    if (!control) return;

    control.setErrors(null);
  }

  public markAsTouched(path?: string): void {
    const control = path ? this.getControl(path) : this.formGroup;
    if (!control) return;

    control.markAsTouched({ onlySelf: false });
  }

  public markAsUntouched(path?: string): void {
    const control = path ? this.getControl(path) : this.formGroup;
    if (!control) return;

    control.markAsUntouched({ onlySelf: false });
  }

  public markAsDirty(path?: string): void {
    const control = path ? this.getControl(path) : this.formGroup;
    if (!control) return;

    control.markAsDirty({ onlySelf: false });
  }

  public markAsPristine(path?: string): void {
    const control = path ? this.getControl(path) : this.formGroup;
    if (!control) return;

    control.markAsPristine({ onlySelf: false });
  }

  public enable(path?: string): void {
    const control = path ? this.getControl(path) : this.formGroup;
    if (!control) return;

    control.enable();
  }

  public disable(path?: string): void {
    const control = path ? this.getControl(path) : this.formGroup;
    if (!control) return;

    control.disable();
  }

  public reset(value?: any): void {
    if (!this.formGroup) return;

    this.formGroup.reset(value);
  }

  public validate(path?: string): boolean {
    const control = path ? this.getControl(path) : this.formGroup;
    if (!control) return false;

    control.updateValueAndValidity();
    return control.valid;
  }

  public setSubmitting(isSubmitting: boolean): void {
    const state = this.stateSubject.value;
    this.stateSubject.next({
      ...state,
      isSubmitting,
    });
    this.updateContext();
  }

  public incrementSubmitCount(): void {
    const state = this.stateSubject.value;
    this.stateSubject.next({
      ...state,
      submitCount: state.submitCount + 1,
    });
    this.updateContext();
  }

  public setValidating(isValidating: boolean): void {
    const state = this.stateSubject.value;
    this.stateSubject.next({
      ...state,
      isValidating,
    });
    this.updateContext();
  }

  public setCurrentStep(stepId: string): void {
    const state = this.stateSubject.value;
    const visitedSteps = state.visitedSteps || [];

    if (!visitedSteps.includes(stepId)) {
      visitedSteps.push(stepId);
    }

    this.stateSubject.next({
      ...state,
      currentStep: stepId,
      visitedSteps,
    });
    this.updateContext();
  }

  public markStepCompleted(stepId: string): void {
    const state = this.stateSubject.value;
    const completedSteps = state.completedSteps || [];

    if (!completedSteps.includes(stepId)) {
      completedSteps.push(stepId);
    }

    this.stateSubject.next({
      ...state,
      completedSteps,
    });
    this.updateContext();
  }

  public addArrayItem(path: string, value?: any, index?: number): void {
    const control = this.getControl(path);
    if (!(control instanceof FormArray)) return;

    const newControl = this.createArrayItemControl(value);

    if (index !== undefined) {
      control.insert(index, newControl);
    } else {
      control.push(newControl);
    }
  }

  public removeArrayItem(path: string, index: number): void {
    const control = this.getControl(path);
    if (!(control instanceof FormArray)) return;

    control.removeAt(index);
  }

  public moveArrayItem(path: string, from: number, to: number): void {
    const control = this.getControl(path);
    if (!(control instanceof FormArray)) return;

    const item = control.at(from);
    control.removeAt(from);
    control.insert(to, item);
  }

  private createArrayItemControl(value?: any): AbstractControl {
    return new FormGroup({});
  }

  public setMetadata(key: string, value: any): void {
    this.metadata.set(key, value);
    this.updateContext();
  }

  public getMetadata(key: string): any {
    return this.metadata.get(key);
  }

  public deleteMetadata(key: string): void {
    this.metadata.delete(key);
    this.updateContext();
  }

  public clearMetadata(): void {
    this.metadata.clear();
    this.updateContext();
  }

  public getState(): IFormState {
    return this.stateSubject.value;
  }

  public getForm(): FormGroup | null {
    return this.formGroup;
  }

  public getConfig(): IFormConfig | null {
    return this.formConfig;
  }

  public isValid(): boolean {
    return this.formGroup?.valid || false;
  }

  public isDirty(): boolean {
    return this.formGroup?.dirty || false;
  }

  public isTouched(): boolean {
    return this.formGroup?.touched || false;
  }

  public isPristine(): boolean {
    return this.formGroup?.pristine || false;
  }

  public isSubmitting(): boolean {
    return this.stateSubject.value.isSubmitting;
  }

  private cleanup(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.subscriptions = [];
    this.metadata.clear();
  }

  public destroy(): void {
    this.cleanup();
    this.formGroup = null;
    this.formConfig = null;
    this.stateSubject.next({
      values: {},
      errors: {},
      touched: {},
      dirty: {},
      disabled: {},
      hidden: {},
      isValid: false,
      isSubmitting: false,
      isValidating: false,
      submitCount: 0,
    });
  }
}
