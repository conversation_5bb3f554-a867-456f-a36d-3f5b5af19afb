import { Injectable } from '@angular/core';
import {
  AbstractControl,
  FormGroup,
  FormArray,
  FormControl,
} from '@angular/forms';
import { BehaviorSubject } from 'rxjs';
import { IField } from '.';

export interface FieldRegistration {
  field: IField;
  control: AbstractControl;
  path: string;
  parent?: string;
  children?: string[];
}

@Injectable()
export class FieldRegistry {
  private readonly fields = new Map<string, FieldRegistration>();
  private readonly fieldsByPath = new Map<string, string>();
  private readonly fieldsByName = new Map<string, string[]>();
  private readonly fieldsSubject = new BehaviorSubject<
    Map<string, FieldRegistration>
  >(new Map());

  public fields$ = this.fieldsSubject.asObservable();

  public registerField(
    field: IField,
    control: AbstractControl,
    path: string,
    parent?: string
  ): void {
    const registration: FieldRegistration = {
      field,
      control,
      path,
      parent,
      children: [],
    };

    this.fields.set(field.id, registration);
    this.fieldsByPath.set(path, field.id);

    if (!this.fieldsByName.has(field.name)) {
      this.fieldsByName.set(field.name, []);
    }
    this.fieldsByName.get(field.name)!.push(field.id);

    if (parent) {
      const parentReg = this.fields.get(parent);
      if (parentReg) {
        parentReg.children = parentReg.children || [];
        parentReg.children.push(field.id);
      }
    }

    this.fieldsSubject.next(new Map(this.fields));
  }

  public unregisterField(fieldId: string): void {
    const registration = this.fields.get(fieldId);
    if (!registration) return;

    if (registration.children) {
      registration.children.forEach((childId) => this.unregisterField(childId));
    }

    this.fields.delete(fieldId);
    this.fieldsByPath.delete(registration.path);

    const nameEntries = this.fieldsByName.get(registration.field.name);
    if (nameEntries) {
      const index = nameEntries.indexOf(fieldId);
      if (index > -1) {
        nameEntries.splice(index, 1);
      }
      if (nameEntries.length === 0) {
        this.fieldsByName.delete(registration.field.name);
      }
    }

    if (registration.parent) {
      const parentReg = this.fields.get(registration.parent);
      if (parentReg?.children) {
        const index = parentReg.children.indexOf(fieldId);
        if (index > -1) {
          parentReg.children.splice(index, 1);
        }
      }
    }

    this.fieldsSubject.next(new Map(this.fields));
  }

  public getFieldById(id: string): FieldRegistration | undefined {
    return this.fields.get(id);
  }

  public getFieldByPath(path: string): FieldRegistration | undefined {
    const fieldId = this.fieldsByPath.get(path);
    return fieldId ? this.fields.get(fieldId) : undefined;
  }

  public getFieldsByName(name: string): FieldRegistration[] {
    const fieldIds = this.fieldsByName.get(name) || [];
    return fieldIds
      .map((id) => this.fields.get(id))
      .filter(Boolean) as FieldRegistration[];
  }

  public getControlById(id: string): AbstractControl | undefined {
    return this.fields.get(id)?.control;
  }

  public getControlByPath(path: string): AbstractControl | undefined {
    return this.getFieldByPath(path)?.control;
  }

  public getFieldConfig(id: string): IField | undefined {
    return this.fields.get(id)?.field;
  }

  public updateFieldProperty(
    id: string,
    property: keyof IField,
    value: any
  ): void {
    const registration = this.fields.get(id);
    if (!registration) return;

    (registration.field as any)[property] = value;
    this.fieldsSubject.next(new Map(this.fields));
  }

  public updateFieldProperties(id: string, properties: Partial<IField>): void {
    const registration = this.fields.get(id);
    if (!registration) return;

    Object.assign(registration.field, properties);
    this.fieldsSubject.next(new Map(this.fields));
  }

  public getChildren(parentId: string): FieldRegistration[] {
    const parent = this.fields.get(parentId);
    if (!parent?.children) return [];

    return parent.children
      .map((childId) => this.fields.get(childId))
      .filter(Boolean) as FieldRegistration[];
  }

  public getParent(fieldId: string): FieldRegistration | undefined {
    const field = this.fields.get(fieldId);
    return field?.parent ? this.fields.get(field.parent) : undefined;
  }

  public getSiblings(fieldId: string): FieldRegistration[] {
    const field = this.fields.get(fieldId);
    if (!field?.parent) return [];

    return this.getChildren(field.parent).filter((f) => f.field.id !== fieldId);
  }

  public getAllFields(): FieldRegistration[] {
    return Array.from(this.fields.values());
  }

  public getFieldsInSection(sectionId: string): FieldRegistration[] {
    return this.getAllFields().filter((reg) =>
      reg.path.startsWith(`sections.${sectionId}`)
    );
  }

  public getFieldsInRow(
    sectionId: string,
    rowIndex: number
  ): FieldRegistration[] {
    return this.getAllFields().filter((reg) =>
      reg.path.startsWith(`sections.${sectionId}.rows.${rowIndex}`)
    );
  }

  public getFieldsInColumn(
    sectionId: string,
    rowIndex: number,
    columnIndex: number
  ): FieldRegistration[] {
    return this.getAllFields().filter((reg) =>
      reg.path.startsWith(
        `sections.${sectionId}.rows.${rowIndex}.columns.${columnIndex}`
      )
    );
  }

  public findFieldsByType(type: string): FieldRegistration[] {
    return this.getAllFields().filter((reg) => reg.field.type === type);
  }

  public findFieldsByValidator(validatorName: string): FieldRegistration[] {
    return this.getAllFields().filter((reg) =>
      reg.field.validation?.some((v) => v.type === validatorName)
    );
  }

  public getRequiredFields(): FieldRegistration[] {
    return this.getAllFields().filter((reg) => reg.field.required === true);
  }

  public getConditionalFields(): FieldRegistration[] {
    return this.getAllFields().filter(
      (reg) => reg.field.condition || typeof reg.field.required === 'object'
    );
  }

  public getFieldPath(fieldId: string): string | undefined {
    return this.fields.get(fieldId)?.path;
  }

  public buildFieldPath(
    sectionId: string,
    rowIndex: number,
    columnIndex: number,
    fieldIndex: number
  ): string {
    return `sections.${sectionId}.rows.${rowIndex}.columns.${columnIndex}.fields.${fieldIndex}`;
  }

  public parseFieldPath(path: string): {
    sectionId?: string;
    rowIndex?: number;
    columnIndex?: number;
    fieldIndex?: number;
  } {
    const parts = path.split('.');
    const result: any = {};

    for (let i = 0; i < parts.length; i++) {
      if (parts[i] === 'sections' && parts[i + 1]) {
        result.sectionId = parts[i + 1];
        i++;
      } else if (parts[i] === 'rows' && parts[i + 1]) {
        result.rowIndex = parseInt(parts[i + 1]);
        i++;
      } else if (parts[i] === 'columns' && parts[i + 1]) {
        result.columnIndex = parseInt(parts[i + 1]);
        i++;
      } else if (parts[i] === 'fields' && parts[i + 1]) {
        result.fieldIndex = parseInt(parts[i + 1]);
        i++;
      }
    }

    return result;
  }

  public moveField(fieldId: string, newPath: string): void {
    const registration = this.fields.get(fieldId);
    if (!registration) return;

    this.fieldsByPath.delete(registration.path);
    registration.path = newPath;
    this.fieldsByPath.set(newPath, fieldId);

    this.fieldsSubject.next(new Map(this.fields));
  }

  public cloneField(
    fieldId: string,
    newId: string,
    newPath: string
  ): FieldRegistration | undefined {
    const original = this.fields.get(fieldId);
    if (!original) return undefined;

    const clonedField: IField = {
      ...original.field,
      id: newId,
      name: `${original.field.name}_copy`,
    };

    const clonedControl = this.cloneControl(original.control);

    this.registerField(clonedField, clonedControl, newPath, original.parent);

    return this.fields.get(newId);
  }

  private cloneControl(control: AbstractControl): AbstractControl {
    if (control instanceof FormControl) {
      return new FormControl(
        control.value,
        control.validator,
        control.asyncValidator
      );
    } else if (control instanceof FormGroup) {
      const cloned = new FormGroup({});
      Object.keys(control.controls).forEach((key) => {
        cloned.addControl(key, this.cloneControl(control.controls[key]));
      });
      return cloned;
    } else if (control instanceof FormArray) {
      const cloned = new FormArray([]);
      control.controls.forEach((c) => {
        cloned.push(this.cloneControl(c));
      });
      return cloned;
    }
    return control;
  }

  public clear(): void {
    this.fields.clear();
    this.fieldsByPath.clear();
    this.fieldsByName.clear();
    this.fieldsSubject.next(new Map());
  }

  public getFieldsTree(): any {
    const tree: any = {};

    this.getAllFields()
      .filter((reg) => !reg.parent)
      .forEach((reg) => {
        tree[reg.field.id] = this.buildFieldTree(reg);
      });

    return tree;
  }

  private buildFieldTree(registration: FieldRegistration): any {
    const node: any = {
      id: registration.field.id,
      name: registration.field.name,
      type: registration.field.type,
      path: registration.path,
    };

    if (registration.children && registration.children.length > 0) {
      node.children = registration.children
        .map((childId) => this.fields.get(childId))
        .filter(Boolean)
        .map((child) => this.buildFieldTree(child!));
    }

    return node;
  }

  public validateRegistry(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    this.fields.forEach((registration, id) => {
      if (!registration.control) {
        errors.push(`Field ${id} has no associated control`);
      }

      if (!registration.path) {
        errors.push(`Field ${id} has no path`);
      }

      if (registration.parent && !this.fields.has(registration.parent)) {
        errors.push(
          `Field ${id} references non-existent parent ${registration.parent}`
        );
      }

      if (registration.children) {
        registration.children.forEach((childId) => {
          if (!this.fields.has(childId)) {
            errors.push(`Field ${id} references non-existent child ${childId}`);
          }
        });
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
