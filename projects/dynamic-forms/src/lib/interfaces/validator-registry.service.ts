// core/services/validator-registry.service.ts

import { Injectable } from '@angular/core';
import {
  ValidatorFn,
  AsyncValidatorFn,
  Validators,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { Observable, of } from 'rxjs';
import { map, delay } from 'rxjs/operators';

export interface ValidatorConfig {
  name: string;
  params?: any;
  message?: string;
}

export interface AsyncValidatorConfig extends ValidatorConfig {
  debounce?: number;
  endpoint?: string;
}

export type ValidatorFactory = (params?: any) => ValidatorFn;
export type AsyncValidatorFactory = (params?: any) => AsyncValidatorFn;

@Injectable()
export class ValidatorRegistry {
  private readonly validators = new Map<string, ValidatorFactory>();
  private readonly asyncValidators = new Map<string, AsyncValidatorFactory>();
  private readonly errorMessages = new Map<
    string,
    string | ((params?: any) => string)
  >();

  constructor() {
    this.registerBuiltInValidators();
    this.registerBuiltInAsyncValidators();
    this.registerDefaultErrorMessages();
  }

  private registerBuiltInValidators(): void {
    this.validators.set('required', () => Validators.required);
    this.validators.set('requiredTrue', () => Validators.requiredTrue);
    this.validators.set('email', () => Validators.email);
    this.validators.set('min', (params) =>
      Validators.min(params?.value ?? params)
    );
    this.validators.set('max', (params) =>
      Validators.max(params?.value ?? params)
    );
    this.validators.set('minLength', (params) =>
      Validators.minLength(params?.value ?? params)
    );
    this.validators.set('maxLength', (params) =>
      Validators.maxLength(params?.value ?? params)
    );
    this.validators.set('pattern', (params) =>
      Validators.pattern(params?.value ?? params)
    );

    this.validators.set('url', () => this.urlValidator);
    this.validators.set('phone', () => this.phoneValidator);
    this.validators.set('creditCard', () => this.creditCardValidator);
    this.validators.set('alphanumeric', () => this.alphanumericValidator);
    this.validators.set('numeric', () => this.numericValidator);
    this.validators.set('alpha', () => this.alphaValidator);
    this.validators.set('equalTo', (params) =>
      this.equalToValidator(params?.field ?? params)
    );
    this.validators.set('notEqualTo', (params) =>
      this.notEqualToValidator(params?.field ?? params)
    );
    this.validators.set('minDate', (params) =>
      this.minDateValidator(params?.value ?? params)
    );
    this.validators.set('maxDate', (params) =>
      this.maxDateValidator(params?.value ?? params)
    );
    this.validators.set('dateRange', (params) =>
      this.dateRangeValidator(params?.min, params?.max)
    );
    this.validators.set('fileSize', (params) =>
      this.fileSizeValidator(params?.max ?? params)
    );
    this.validators.set('fileType', (params) =>
      this.fileTypeValidator(params?.accept ?? params)
    );
    this.validators.set('imageRatio', (params) =>
      this.imageRatioValidator(params?.ratio ?? params)
    );
    this.validators.set('strongPassword', () => this.strongPasswordValidator);
    this.validators.set('contains', (params) =>
      this.containsValidator(params?.value ?? params)
    );
    this.validators.set('notContains', (params) =>
      this.notContainsValidator(params?.value ?? params)
    );
    this.validators.set('startsWith', (params) =>
      this.startsWithValidator(params?.value ?? params)
    );
    this.validators.set('endsWith', (params) =>
      this.endsWithValidator(params?.value ?? params)
    );
    this.validators.set('json', () => this.jsonValidator);
    this.validators.set('ipAddress', (params) =>
      this.ipAddressValidator(params?.version)
    );
    this.validators.set('macAddress', () => this.macAddressValidator);
    this.validators.set('uuid', () => this.uuidValidator);
    this.validators.set('minAge', (params) =>
      this.minAgeValidator(params?.value ?? params)
    );
    this.validators.set('maxAge', (params) =>
      this.maxAgeValidator(params?.value ?? params)
    );
  }

  private registerBuiltInAsyncValidators(): void {
    this.asyncValidators.set('unique', (params) =>
      this.uniqueValidator(params)
    );
    this.asyncValidators.set('exists', (params) =>
      this.existsValidator(params)
    );
    this.asyncValidators.set('api', (params) => this.apiValidator(params));
  }

  private registerDefaultErrorMessages(): void {
    this.errorMessages.set('required', 'This field is required');
    this.errorMessages.set('requiredTrue', 'This field must be checked');
    this.errorMessages.set('email', 'Please enter a valid email address');
    this.errorMessages.set(
      'min',
      (params) => `Value must be at least ${params?.value ?? params}`
    );
    this.errorMessages.set(
      'max',
      (params) => `Value must not exceed ${params?.value ?? params}`
    );
    this.errorMessages.set(
      'minLength',
      (params) => `Minimum ${params?.value ?? params} characters required`
    );
    this.errorMessages.set(
      'maxLength',
      (params) => `Maximum ${params?.value ?? params} characters allowed`
    );
    this.errorMessages.set('pattern', 'Invalid format');
    this.errorMessages.set('url', 'Please enter a valid URL');
    this.errorMessages.set('phone', 'Please enter a valid phone number');
    this.errorMessages.set(
      'creditCard',
      'Please enter a valid credit card number'
    );
    this.errorMessages.set(
      'alphanumeric',
      'Only letters and numbers are allowed'
    );
    this.errorMessages.set('numeric', 'Only numbers are allowed');
    this.errorMessages.set('alpha', 'Only letters are allowed');
    this.errorMessages.set(
      'equalTo',
      (params) => `Value must match ${params?.field ?? params}`
    );
    this.errorMessages.set(
      'notEqualTo',
      (params) => `Value must not match ${params?.field ?? params}`
    );
    this.errorMessages.set(
      'minDate',
      (params) => `Date must be after ${params?.value ?? params}`
    );
    this.errorMessages.set(
      'maxDate',
      (params) => `Date must be before ${params?.value ?? params}`
    );
    this.errorMessages.set(
      'dateRange',
      'Date must be within the specified range'
    );
    this.errorMessages.set(
      'fileSize',
      (params) =>
        `File size must not exceed ${this.formatFileSize(
          params?.max ?? params
        )}`
    );
    this.errorMessages.set('fileType', 'Invalid file type');
    this.errorMessages.set(
      'strongPassword',
      'Password must contain uppercase, lowercase, number and special character'
    );
    this.errorMessages.set('json', 'Invalid JSON format');
    this.errorMessages.set('ipAddress', 'Invalid IP address');
    this.errorMessages.set('macAddress', 'Invalid MAC address');
    this.errorMessages.set('uuid', 'Invalid UUID format');
    this.errorMessages.set(
      'minAge',
      (params) => `Must be at least ${params?.value ?? params} years old`
    );
    this.errorMessages.set(
      'maxAge',
      (params) => `Must be under ${params?.value ?? params} years old`
    );
    this.errorMessages.set('unique', 'This value already exists');
    this.errorMessages.set('exists', 'This value does not exist');
  }

  public registerValidator(name: string, factory: ValidatorFactory): void {
    this.validators.set(name, factory);
  }

  public registerAsyncValidator(
    name: string,
    factory: AsyncValidatorFactory
  ): void {
    this.asyncValidators.set(name, factory);
  }

  public registerErrorMessage(
    name: string,
    message: string | ((params?: any) => string)
  ): void {
    this.errorMessages.set(name, message);
  }

  public getValidator(name: string): ValidatorFactory | undefined {
    return this.validators.get(name);
  }

  public getAsyncValidator(name: string): AsyncValidatorFactory | undefined {
    return this.asyncValidators.get(name);
  }

  public getErrorMessage(name: string, params?: any): string {
    const message = this.errorMessages.get(name);
    if (typeof message === 'function') {
      return message(params);
    }
    return message || `Validation failed: ${name}`;
  }

  public buildValidators(configs: ValidatorConfig[]): ValidatorFn[] {
    return configs
      .map((config) => {
        const factory = this.validators.get(config.name);
        return factory ? factory(config.params) : null;
      })
      .filter((validator) => validator !== null);
  }

  public buildAsyncValidators(
    configs: AsyncValidatorConfig[]
  ): AsyncValidatorFn[] {
    return configs
      .map((config) => {
        const factory = this.asyncValidators.get(config.name);
        return factory ? factory(config.params) : null;
      })
      .filter((validator) => validator !== null);
  }

  private readonly urlValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control.value) return null;
    const urlPattern =
      /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    return urlPattern.test(control.value) ? null : { url: true };
  };

  private readonly phoneValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control.value) return null;
    const phonePattern =
      /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/;
    return phonePattern.test(control.value) ? null : { phone: true };
  };

  private readonly creditCardValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control.value) return null;
    const value = control.value.replace(/\s/g, '');
    if (!/^\d{13,19}$/.test(value)) return { creditCard: true };

    let sum = 0;
    let isEven = false;
    for (let i = value.length - 1; i >= 0; i--) {
      let digit = parseInt(value[i], 10);
      if (isEven) {
        digit *= 2;
        if (digit > 9) digit -= 9;
      }
      sum += digit;
      isEven = !isEven;
    }
    return sum % 10 === 0 ? null : { creditCard: true };
  };

  private readonly alphanumericValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control.value) return null;
    return /^[a-zA-Z0-9]+$/.test(control.value) ? null : { alphanumeric: true };
  };

  private readonly numericValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control.value) return null;
    return /^\d+$/.test(control.value) ? null : { numeric: true };
  };

  private readonly alphaValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control.value) return null;
    return /^[a-zA-Z]+$/.test(control.value) ? null : { alpha: true };
  };

  private equalToValidator(fieldName: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.parent) return null;
      const field = control.parent.get(fieldName);
      if (!field) return null;
      return control.value === field.value
        ? null
        : { equalTo: { field: fieldName } };
    };
  }

  private notEqualToValidator(fieldName: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.parent) return null;
      const field = control.parent.get(fieldName);
      if (!field) return null;
      return control.value !== field.value
        ? null
        : { notEqualTo: { field: fieldName } };
    };
  }

  private minDateValidator(minDate: string | Date): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const date = new Date(control.value);
      const min = this.parseDate(minDate);
      return date >= min ? null : { minDate: { min: minDate } };
    };
  }

  private maxDateValidator(maxDate: string | Date): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const date = new Date(control.value);
      const max = this.parseDate(maxDate);
      return date <= max ? null : { maxDate: { max: maxDate } };
    };
  }

  private dateRangeValidator(
    minDate: string | Date,
    maxDate: string | Date
  ): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const date = new Date(control.value);
      const min = this.parseDate(minDate);
      const max = this.parseDate(maxDate);
      return date >= min && date <= max
        ? null
        : { dateRange: { min: minDate, max: maxDate } };
    };
  }

  private fileSizeValidator(maxSize: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const files = Array.isArray(control.value)
        ? control.value
        : [control.value];
      const oversized = files.some((file: File) => file.size > maxSize);
      return oversized ? { fileSize: { max: maxSize } } : null;
    };
  }

  private fileTypeValidator(accept: string | string[]): ValidatorFn {
    const acceptedTypes = Array.isArray(accept) ? accept : [accept];
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const files = Array.isArray(control.value)
        ? control.value
        : [control.value];
      const invalid = files.some((file: File) => {
        const ext = file.name.split('.').pop()?.toLowerCase();
        return !acceptedTypes.some((type) => {
          if (type.startsWith('.')) return type.slice(1) === ext;
          if (type.includes('*'))
            return file.type.startsWith(type.replace('*', ''));
          return file.type === type;
        });
      });
      return invalid ? { fileType: { accept } } : null;
    };
  }

  private imageRatioValidator(ratio: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      return null;
    };
  }

  private readonly strongPasswordValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control.value) return null;
    const hasUpper = /[A-Z]/.test(control.value);
    const hasLower = /[a-z]/.test(control.value);
    const hasNumber = /\d/.test(control.value);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(control.value);
    return hasUpper && hasLower && hasNumber && hasSpecial
      ? null
      : { strongPassword: true };
  };

  private containsValidator(value: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      return control.value.includes(value) ? null : { contains: { value } };
    };
  }

  private notContainsValidator(value: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      return !control.value.includes(value) ? null : { notContains: { value } };
    };
  }

  private startsWithValidator(value: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      return control.value.startsWith(value) ? null : { startsWith: { value } };
    };
  }

  private endsWithValidator(value: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      return control.value.endsWith(value) ? null : { endsWith: { value } };
    };
  }

  private readonly jsonValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control.value) return null;
    try {
      JSON.parse(control.value);
      return null;
    } catch {
      return { json: true };
    }
  };

  private ipAddressValidator(version?: 4 | 6): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const ipv4Pattern =
        /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
      const ipv6Pattern =
        /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;

      if (version === 4)
        return ipv4Pattern.test(control.value) ? null : { ipAddress: true };
      if (version === 6)
        return ipv6Pattern.test(control.value) ? null : { ipAddress: true };
      return ipv4Pattern.test(control.value) || ipv6Pattern.test(control.value)
        ? null
        : { ipAddress: true };
    };
  }

  private readonly macAddressValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control.value) return null;
    const pattern = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
    return pattern.test(control.value) ? null : { macAddress: true };
  };

  private readonly uuidValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control.value) return null;
    const pattern =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return pattern.test(control.value) ? null : { uuid: true };
  };

  private minAgeValidator(minAge: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const birthDate = new Date(control.value);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      const dayDiff = today.getDate() - birthDate.getDate();
      const actualAge =
        monthDiff < 0 || (monthDiff === 0 && dayDiff < 0) ? age - 1 : age;
      return actualAge >= minAge ? null : { minAge: { value: minAge } };
    };
  }

  private maxAgeValidator(maxAge: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      const birthDate = new Date(control.value);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      const dayDiff = today.getDate() - birthDate.getDate();
      const actualAge =
        monthDiff < 0 || (monthDiff === 0 && dayDiff < 0) ? age - 1 : age;
      return actualAge <= maxAge ? null : { maxAge: { value: maxAge } };
    };
  }

  private uniqueValidator(params: any): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value) return of(null);
      return of(null).pipe(
        delay(params?.debounce || 300),
        map(() => (Math.random() > 0.5 ? null : { unique: true }))
      );
    };
  }

  private existsValidator(params: any): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value) return of(null);
      return of(null).pipe(
        delay(params?.debounce || 300),
        map(() => (Math.random() > 0.5 ? null : { exists: true }))
      );
    };
  }

  private apiValidator(params: any): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      if (!control.value) return of(null);
      return of(null).pipe(
        delay(params?.debounce || 300),
        map(() => null)
      );
    };
  }

  private parseDate(date: string | Date): Date {
    if (date instanceof Date) return date;
    if (date === 'today') return new Date();
    if (date === 'yesterday') {
      const d = new Date();
      d.setDate(d.getDate() - 1);
      return d;
    }
    if (date === 'tomorrow') {
      const d = new Date();
      d.setDate(d.getDate() + 1);
      return d;
    }
    return new Date(date);
  }

  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return Math.round((bytes / Math.pow(k, i)) * 100) / 100 + ' ' + sizes[i];
  }
}
